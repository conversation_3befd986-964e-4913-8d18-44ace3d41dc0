# City Insights AI

AI-powered urban analytics platform using public datasets, Gemini AI, and MongoDB vector search.

## 🏆 MongoDB Track - Google Cloud Hackathon

### Overview
City Insights AI helps users discover and compare neighborhoods using advanced AI and vector search technology. Find similar areas, get AI-powered insights, and make informed decisions about where to live or invest.

### 🚀 Features
- **Neighborhood Comparator**: Find similar neighborhoods using MongoDB vector search
- **AI Chat Interface**: Natural language queries powered by Gemini AI
- **Trend Predictor**: Analyze historical data for future insights
- **Interactive Dashboard**: Visual exploration with maps and charts

### 🛠 Tech Stack
- **Frontend**: React.js with TypeScript
- **Backend**: Node.js with Express.js
- **Database**: MongoDB Atlas with Vector Search
- **AI**: Google Cloud Vertex AI (Gemini Pro/Flash)
- **Maps**: Google Maps JavaScript API
- **Charts**: Chart.js
- **Hosting**: Google Cloud Run

### 📁 Project Structure
```
city-insights-ai/
├── frontend/          # React.js application
├── backend/           # Node.js API server
├── data/             # Data processing scripts
├── docker-compose.yml
└── cloudbuild.yaml
```

### 🚀 Quick Start

#### Prerequisites
- Node.js 18+
- MongoDB Atlas account
- Google Cloud Platform account
- Google Maps API key

#### Installation
```bash
# Clone and setup
git clone <repository>
cd city-insights-ai

# Install dependencies
cd backend && npm install
cd ../frontend && npm install

# Setup environment variables
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# Start development servers
npm run dev
```

#### Environment Variables
```env
# Google Cloud
GOOGLE_CLOUD_PROJECT_ID=your-project-id
VERTEX_AI_LOCATION=us-central1

# MongoDB
MONGODB_URI=mongodb+srv://username:<EMAIL>/cityinsights

# Google Maps
GOOGLE_MAPS_API_KEY=your-maps-api-key
```

### 📊 API Endpoints
- `GET /api/neighborhoods` - List all neighborhoods
- `POST /api/neighborhoods/similar` - Find similar neighborhoods
- `POST /api/chat` - AI chat interface
- `GET /api/analytics/trends/:neighborhood` - Get trend data
- `POST /api/analytics/compare` - Compare neighborhoods

### 🎯 Demo Features
1. **Interactive Map**: Explore neighborhoods visually
2. **AI Chat**: "Find family-friendly areas under $3500/month"
3. **Similarity Search**: Discover neighborhoods like SoHo or Greenwich Village
4. **Trend Analysis**: Visualize crime, housing, and demographic trends

### 🏗 Development Phases
- **Phase 1**: Foundation & Data Models
- **Phase 2**: Core Features & AI Integration
- **Phase 3**: Frontend & Visualization
- **Phase 4**: Deployment & Polish

### 📈 Success Metrics
- All core features functional
- API responses under 2 seconds
- Intuitive user experience
- Meaningful AI insights
- Scalable architecture

### 🎬 Demo Video
[Link to 3-minute demo showcasing all features]

### 🏆 Winning Factors
- Technical excellence with proper vector search + AI
- Solves real problems for real users
- Professional, modern interface
- Clear scalability path
- Creative use of AI for urban insights

---

Built for the MongoDB Track - Google Cloud Hackathon 2025