const express = require('express');
const { body, validationResult } = require('express-validator');
const geminiService = require('../services/geminiService');
const vectorSearchService = require('../services/vectorSearchService');
const Neighborhood = require('../models/Neighborhood');
const { logger } = require('../utils/logger');

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

/**
 * POST /api/chat
 * Handle chat messages and provide AI responses
 */
router.post('/', [
  body('message').isString().trim().isLength({ min: 1, max: 1000 }),
  body('context').optional().isArray({ max: 10 }),
  body('sessionId').optional().isString().trim(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { message, context = [], sessionId } = req.body;

    logger.info(`Chat request - Session: ${sessionId}, Message: ${message}`);

    // Analyze the message to determine if it's asking for neighborhood recommendations
    const isNeighborhoodQuery = await analyzeMessageIntent(message);
    
    let relevantData = null;
    let response;

    if (isNeighborhoodQuery.isSearchQuery) {
      // Extract search criteria from the message
      const criteria = extractSearchCriteria(message);
      
      if (Object.keys(criteria).length > 0) {
        // Get relevant neighborhoods based on criteria
        const searchResults = await vectorSearchService.getRecommendations(criteria);
        relevantData = searchResults.neighborhoods;
        
        // Generate enhanced response with comprehensive data
        response = await geminiService.generateEnhancedChatResponse(
          message,
          context,
          relevantData[0] // Use first neighborhood for comprehensive data
        );
      } else {
        // Use vector search with the message as query
        logger.info(`Performing vector search for: "${message}"`);
        const searchResults = await vectorSearchService.searchByQuery(message, 5);
        logger.info(`Vector search returned ${searchResults.length} results`);
        relevantData = searchResults;

        response = await geminiService.generateEnhancedChatResponse(
          message,
          context,
          relevantData[0] // Use first result for comprehensive data
        );
      }
    } else {
      // General chat response without specific neighborhood data
      response = await geminiService.generateEnhancedChatResponse(message, context);
    }

    // Log the interaction
    logger.info(`Chat response generated for session: ${sessionId}`);

    res.json({
      response,
      relevantNeighborhoods: relevantData ? relevantData.slice(0, 5) : null,
      intent: isNeighborhoodQuery,
      sessionId
    });

  } catch (error) {
    logger.error('Error processing chat message:', error);
    res.status(500).json({
      error: 'Failed to process chat message',
      message: error.message
    });
  }
});

/**
 * POST /api/chat/neighborhood-query
 * Specialized endpoint for neighborhood-specific queries
 */
router.post('/neighborhood-query', [
  body('query').isString().trim().isLength({ min: 1, max: 500 }),
  body('filters').optional().isObject(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { query, filters = {} } = req.body;

    // Combine query with filters for search
    const searchCriteria = {
      ...extractSearchCriteria(query),
      ...filters
    };

    const recommendations = await vectorSearchService.getRecommendations(searchCriteria);

    // Generate detailed explanation
    const explanation = await geminiService.generateChatResponse(
      `Explain why these neighborhoods match the query: "${query}"`,
      [],
      recommendations.neighborhoods
    );

    res.json({
      query,
      neighborhoods: recommendations.neighborhoods,
      explanation,
      totalFound: recommendations.totalFound,
      searchCriteria
    });

  } catch (error) {
    logger.error('Error processing neighborhood query:', error);
    res.status(500).json({
      error: 'Failed to process neighborhood query',
      message: error.message
    });
  }
});

/**
 * GET /api/chat/suggestions
 * Get suggested questions/queries for users
 */
router.get('/suggestions', async (req, res) => {
  try {
    const suggestions = [
      "Find family-friendly neighborhoods under R3500/month",
      "Show me safe areas with good public transportation",
      "What are the best neighborhoods for young professionals?",
      "Find quiet residential areas with parks",
      "Which neighborhoods have the best restaurants and nightlife?",
      "Show me affordable areas in Brooklyn",
      "Find neighborhoods similar to Greenwich Village",
      "What are the safest neighborhoods in Manhattan?",
      "Show me areas with good schools and low crime",
      "Find trendy neighborhoods with cultural attractions"
    ];

    res.json({
      suggestions,
      categories: [
        { name: 'Family-Friendly', keywords: ['family', 'schools', 'parks', 'safe'] },
        { name: 'Young Professionals', keywords: ['trendy', 'nightlife', 'restaurants', 'transit'] },
        { name: 'Budget-Conscious', keywords: ['affordable', 'cheap', 'budget', 'under'] },
        { name: 'Safety-Focused', keywords: ['safe', 'low crime', 'secure'] },
        { name: 'Transit-Oriented', keywords: ['subway', 'transportation', 'walkable'] }
      ]
    });
  } catch (error) {
    logger.error('Error fetching chat suggestions:', error);
    res.status(500).json({
      error: 'Failed to fetch suggestions',
      message: error.message
    });
  }
});

/**
 * Analyze message intent to determine if it's asking for neighborhood recommendations
 * @param {string} message - User message
 * @returns {Object} - Intent analysis
 */
async function analyzeMessageIntent(message) {
  const lowerMessage = message.toLowerCase();

  // Keywords that indicate neighborhood search
  const searchKeywords = [
    'find', 'show', 'recommend', 'suggest', 'looking for', 'need', 'want',
    'neighborhood', 'area', 'place', 'location', 'where', 'best'
  ];

  const locationKeywords = [
    'manhattan', 'brooklyn', 'queens', 'bronx', 'staten island',
    'neighborhood', 'area', 'district', 'borough', 'city', 'cities'
  ];

  const criteriaKeywords = [
    'cheap', 'affordable', 'expensive', 'safe', 'dangerous', 'family',
    'young', 'professional', 'quiet', 'loud', 'trendy', 'hip', 'cultural',
    'restaurants', 'nightlife', 'schools', 'parks', 'transit', 'subway',
    'kids', 'children', 'raise', 'budget', 'rent', 'cost', 'price'
  ];

  // Budget/price patterns that indicate housing search - support USD and ZAR
  const budgetPattern = /[\$r]?\d+(?:,\d{3})*(?:\s*(?:per month|\/month|monthly|budget|rent|rand|zar|dollars?))?/i;
  const hasBudgetMention = budgetPattern.test(message);

  // Family-related patterns
  const familyPattern = /family\s+of\s+\d+|couple|kids|children|raising|schools/i;
  const hasFamilyMention = familyPattern.test(message);

  const hasSearchKeyword = searchKeywords.some(keyword => lowerMessage.includes(keyword));
  const hasLocationKeyword = locationKeywords.some(keyword => lowerMessage.includes(keyword));
  const hasCriteriaKeyword = criteriaKeywords.some(keyword => lowerMessage.includes(keyword));

  // More flexible search detection
  const isImplicitSearch = (hasBudgetMention && hasFamilyMention) ||
                          (hasBudgetMention && hasCriteriaKeyword) ||
                          (hasFamilyMention && hasCriteriaKeyword);

  const isSearchQuery = hasSearchKeyword && (hasLocationKeyword || hasCriteriaKeyword) || isImplicitSearch;

  let confidence = 0;
  if (hasSearchKeyword) confidence += 0.4;
  if (hasLocationKeyword) confidence += 0.3;
  if (hasCriteriaKeyword) confidence += 0.3;
  if (hasBudgetMention) confidence += 0.2;
  if (hasFamilyMention) confidence += 0.2;
  if (isImplicitSearch) confidence += 0.3;

  return {
    isSearchQuery,
    hasLocationKeyword,
    hasCriteriaKeyword,
    confidence: Math.min(confidence, 1.0) // Cap at 1.0
  };
}

/**
 * Extract search criteria from natural language message
 * @param {string} message - User message
 * @returns {Object} - Extracted criteria
 */
function extractSearchCriteria(message) {
  const lowerMessage = message.toLowerCase();
  const criteria = {};

  // Extract price criteria - support both USD ($) and ZAR (R) currencies
  const pricePatterns = [
    { pattern: /under\s*r(\d+(?:,\d{3})*)/i, currency: 'R' },  // "under R3500"
    { pattern: /under\s*\$(\d+(?:,\d{3})*)/i, currency: '$' },  // "under $3500"
    { pattern: /r(\d+(?:,\d{3})*)\s*(?:per month|\/month|monthly|budget)/i, currency: 'R' },  // "R3500 per month"
    { pattern: /\$(\d+(?:,\d{3})*)\s*(?:per month|\/month|monthly|budget)/i, currency: '$' },  // "$3500 per month"
    { pattern: /budget\s*(?:of\s*)?r(\d+(?:,\d{3})*)/i, currency: 'R' },  // "budget R3500"
    { pattern: /budget\s*(?:of\s*)?\$(\d+(?:,\d{3})*)/i, currency: '$' },  // "budget $3500"
    { pattern: /(\d+(?:,\d{3})*)\s*(?:rand|zar)\s*(?:per month|monthly|budget)?/i, currency: 'R' },  // "3500 rand"
    { pattern: /(\d+(?:,\d{3})*)\s*dollars?\s*(?:per month|monthly|budget)?/i, currency: '$' },  // "3500 dollars"
    { pattern: /under\s*(\d+(?:,\d{3})*)/i, currency: '$' }  // fallback "under 3500" assumes USD
  ];

  for (const { pattern, currency } of pricePatterns) {
    const priceMatch = lowerMessage.match(pattern);
    if (priceMatch) {
      criteria.maxRent = parseInt(priceMatch[1].replace(/,/g, ''));
      criteria.currency = currency;
      break; // Use the first match found
    }
  }

  // Extract borough/area - support both NYC and Cape Town areas
  const areas = [
    // NYC boroughs
    'manhattan', 'brooklyn', 'queens', 'bronx', 'staten island',
    // Cape Town areas
    'atlantic seaboard', 'city bowl', 'southern suburbs', 'northern suburbs',
    'west coast', 'cape flats', 'helderberg', 'cape peninsula'
  ];
  const foundArea = areas.find(area => lowerMessage.includes(area));
  if (foundArea) {
    criteria.borough = foundArea;
  }

  // Extract characteristics
  if (lowerMessage.includes('family') || lowerMessage.includes('kids') || lowerMessage.includes('children')) {
    criteria.familyFriendly = true;
  }

  if (lowerMessage.includes('young professional') || lowerMessage.includes('professional')) {
    criteria.youngProfessionals = true;
  }

  if (lowerMessage.includes('safe') || lowerMessage.includes('safety') || lowerMessage.includes('secure')) {
    criteria.minSafetyScore = 7;
  }

  if (lowerMessage.includes('quiet') || lowerMessage.includes('peaceful')) {
    criteria.quiet = true;
  }

  if (lowerMessage.includes('transit') || lowerMessage.includes('subway') || lowerMessage.includes('transportation')) {
    criteria.transitAccess = true;
  }

  if (lowerMessage.includes('cultural') || lowerMessage.includes('arts') || lowerMessage.includes('museum')) {
    criteria.cultural = true;
  }

  return criteria;
}

module.exports = router;
